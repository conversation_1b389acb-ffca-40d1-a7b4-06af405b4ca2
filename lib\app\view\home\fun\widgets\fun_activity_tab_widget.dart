﻿import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_flutter_test1/app/repository/modals/fun/fun_tab_config.dart';

import '../../../../provider/fun/fun_list_provider.dart';

class FunActivityTabWidget extends ConsumerWidget {
  final Function(Map<String, dynamic>? mainTab, Map<String, dynamic>? childTab,
      Map<String, dynamic>? sort)? onTabChange;
  final VoidCallback? onSearch;

  const FunActivityTabWidget({
    super.key,
    this.onTabChange,
    this.onSearch,
  });

  bool _isSameSortOption(FunSortOption? a, FunSortOption? b) {
    if (a == null || b == null) {
      return false;
    }
    return a.sortField == b.sortField &&
        a.ascDescOrder == b.ascDescOrder &&
        a.sortValue == b.sortValue &&
        a.sortName == b.sortName;
  }

  void _onSortPress(BuildContext context, WidgetRef ref, int index) {
    // 模拟排序数据List<FunSortOption>? sort
    List<FunSortOption>? sortData = ref
        .watch(manageFunTabConfigProvider)
        .value
        ?.childrenTab
        .tabs[index]
        .sort;
    final FunSortOption? selectedSort =
        ref.watch(manageFunTabConfigProvider).value?.currentSort;
    if (sortData != null && sortData.isNotEmpty) {
      showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: sortData.map((e) {
                return ListTile(
                  title: Text(e.sortName ?? ''),
                  trailing: _isSameSortOption(selectedSort, e)
                      ? const Icon(Icons.check, color: Colors.red)
                      : null,
                  onTap: () {
                    ref
                        .read(manageFunTabConfigProvider.notifier)
                        .setCurrentSort(e);

                    Navigator.pop(context);
                  },
                );
              }).toList(),
            ),
          );
        },
      );
    }
  }

  /// 主标签
  Widget _buildTabItem(
      WidgetRef ref, FunTabConfig item, int index, int selectedIndex) {
    return GestureDetector(
      onTap: () {
        ref
            .read(manageFunTabConfigProvider.notifier)
            .getChildrenTab(item.code ?? "");
        if (selectedIndex != index) {
          ref.read(tabSelectionStateProvider.notifier).setMainTabIndex(index);
        }
      },
      child: Column(
        children: [
          if (item.iconUrl != null)
            Image.network(
              item.iconUrl ?? '',
              width: 68,
              height: 23,
              errorBuilder: (context, error, stackTrace) =>
                  Text(item.name ?? '',
                      style: TextStyle(
                        color: selectedIndex == index
                            ? const Color(0xFF2F2F2E)
                            : const Color(0xFF676764),
                        fontWeight: selectedIndex == index
                            ? FontWeight.bold
                            : FontWeight.normal,
                        fontSize: 14,
                      )),
            )
          else
            Text(
              item.name ?? '',
              style: TextStyle(
                color: selectedIndex == index
                    ? const Color(0xFF2F2F2E)
                    : const Color(0xFF676764),
                fontWeight: selectedIndex == index
                    ? FontWeight.bold
                    : FontWeight.normal,
                fontSize: 14,
              ),
            ),
          Container(
            margin: const EdgeInsets.only(top: 2),
            width: 40,
            height: 3,
            decoration: BoxDecoration(
              color: selectedIndex == index
                  ? const Color(0xFFF93324)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(3),
            ),
          ),
        ],
      ),
    );
  }

  // 子标签
  Widget _buildChildTabItem(
      WidgetRef ref, FunTabConfig item, int index, int selectedIndex) {
    return GestureDetector(
      onTap: () {
        if (selectedIndex != index) {
          ref.read(tabSelectionStateProvider.notifier).setChildTabIndex(index);
        }
      },
      child: Container(
        margin: const EdgeInsets.fromLTRB(5, 12, 5, 0),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14),
          border: Border.all(color: const Color(0xFFBAA38C), width: 0.5),
          color: selectedIndex == index
              ? const Color(0xFFFFFAED)
              : Colors.transparent,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              item.tabName ?? "",
              style: TextStyle(
                color: selectedIndex == index
                    ? const Color(0xFFFE7801)
                    : const Color(0xFF676764),
                fontSize: 10,
              ),
            ),
            if (item.children != null)
              Container(
                margin: const EdgeInsets.only(left: 5, top: 5),
                child: CustomPaint(
                  size: const Size(8, 8),
                  painter: ArrowPainter(
                    color: selectedIndex == index
                        ? const Color(0xFFFE7801)
                        : const Color(0xFF676764),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabState = ref.watch(manageFunTabConfigProvider);
    final tabSelection = ref.watch(tabSelectionStateProvider);

    return tabState.when(
      data: (data) {
        if (data == null) {
          return const Center(
            child: Text("加载失败"),
          );
        }
        return _buildTab(context, ref, data, tabSelection);
      },
      error: (error, stackTrace) {
        return const Text("error");
      },
      loading: () {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
  }

  Widget _buildTab(BuildContext context, WidgetRef ref, TabStateConfig data,
      TabSelection tabSelection) {
    return Container(
      color: const Color(0xFFF4F4F2),
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 主标签栏
          Container(
              padding: const EdgeInsets.fromLTRB(14, 12, 14, 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: data.mainTab.tabs.map((e) {
                  return _buildTabItem(ref, e, data.mainTab.tabs.indexOf(e),
                      tabSelection.mainTabIndex);
                }).toList(),
              )),
          // 搜索框
          _buildSearchBar(),

          // 第二部分：子标签和排序区域
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: _buildChildrenTabAndSort(context, ref, data, tabSelection),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return GestureDetector(
      onTap: onSearch,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 10),
        padding: const EdgeInsets.only(left: 14),
        decoration: BoxDecoration(
          color: const Color(0xFFFEFFFF),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            Image.asset(
              'assets/images/searchIcon.png',
              width: 13,
              height: 13,
              color: const Color(0xFFF93324),
              errorBuilder: (context, error, stackTrace) => const Icon(
                Icons.search,
                size: 13,
                color: Color(0xFFF93324),
              ),
            ),
            const SizedBox(width: 10),
            const Expanded(
              child: Text(
                '搜索更多优惠，下单享返现',
                style: TextStyle(
                  color: Color(0xFF999999),
                  fontSize: 14,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Container(
              margin: const EdgeInsets.all(3),
              width: 52,
              height: 26,
              decoration: BoxDecoration(
                color: const Color(0xFFF93324),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Center(
                child: Text(
                  '搜索',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 子标签栏和排序区域
  /// 使用 Wrap 布局确保内容能够自适应换行，支持动态高度
  Widget _buildChildrenTabAndSort(BuildContext context, WidgetRef ref,
      TabStateConfig data, TabSelection tabSelection) {
    return Row(
      children: [
        Expanded(
          child: Wrap(
            children: [
              // 子标签列表
              ...data.childrenTab.tabs.map((e) {
                return _buildChildTabItem(
                    ref,
                    e,
                    data.childrenTab.tabs.indexOf(e),
                    tabSelection.childTabIndex);
              }),
              // 排序按钮（如果存在）
              if (data.currentSort != null)
                GestureDetector(
                  onTap: () => _onSortPress(
                      context,
                      ref,
                      data.childrenTab.tabs.indexOf(
                          data.childrenTab.tabs[tabSelection.childTabIndex])),
                  child: Container(
                    margin: const EdgeInsets.fromLTRB(5, 12, 5, 0),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          data.currentSort?.sortName ?? "",
                          style: const TextStyle(
                            fontSize: 11,
                            color: Color(0xFF676764),
                          ),
                        ),
                        const SizedBox(width: 5),
                        CustomPaint(
                          size: const Size(8, 8),
                          painter: ArrowPainter(color: const Color(0xFF676764)),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}

class ArrowPainter extends CustomPainter {
  final Color color;

  ArrowPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(0, 0)
      ..lineTo(size.width, 0)
      ..lineTo(size.width / 2, size.height)
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
